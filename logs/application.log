2025-08-12 13:53:27.454 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 13:53:27.623 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 13:53:27.759 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 13:53:28.008 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 13:53:28.009 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 13:53:28.010 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 13:53:28.011 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:53:28.013 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:53:28.015 [JavaFX Application Thread] ERROR com.zaxxer.hikari.HikariConfig - Failed to load driver class org.sqlite.JDBC from HikariConfig class classloader jdk.internal.loader.ClassLoaders$AppClassLoader@1bc6a36e
2025-08-12 13:53:28.015 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化失败
java.lang.RuntimeException: Failed to load driver class org.sqlite.JDBC in either of HikariConfig class loader or Thread context classloader
	at com.logictrue.merged.module@1.0/com.zaxxer.hikari.HikariConfig.setDriverClassName(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createDataSource(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:53:28.016 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/com.logictrue/fxml/main.fxml

	at javafx.fxml@21/javafx.fxml.FXMLLoader.constructLoadException(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: MyBatis-Plus初始化失败
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	... 13 common frames omitted
Caused by: java.lang.RuntimeException: Failed to load driver class org.sqlite.JDBC in either of HikariConfig class loader or Thread context classloader
	at com.logictrue.merged.module@1.0/com.zaxxer.hikari.HikariConfig.setDriverClassName(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createDataSource(Unknown Source)
	... 20 common frames omitted
2025-08-12 13:55:21.791 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 13:55:21.968 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 13:55:22.098 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 13:55:22.329 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 13:55:22.330 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 13:55:22.331 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 13:55:22.332 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:55:22.335 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:55:22.347 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 13:55:22.470 [JavaFX Application Thread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@552af74b
2025-08-12 13:55:22.471 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 13:55:22.715 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionBasicFieldMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionBasicFieldMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.717 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableHeaderMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableHeaderMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.718 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableDataMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableDataMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.729 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 13:55:22.730 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-12 13:55:22.734 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:55:22.735 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:55:22.737 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=20
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 已存在，跳过创建
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 已存在，跳过创建
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 已存在，跳过创建
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 已存在，跳过创建
2025-08-12 13:55:22.744 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-12 13:55:22.750 [JavaFX Application Thread] INFO  c.l.s.DatabaseInitializationService - 使用动态表创建服务初始化数据库表结构成功
2025-08-12 13:55:22.751 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库表结构初始化完成
2025-08-12 13:55:23.029 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-12 13:55:24.784 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-12 13:55:24.785 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-12 13:55:35.745 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-12 13:55:35.745 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-12 13:55:35.746 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-12 13:55:37.334 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据采集按钮Action事件被触发
2025-08-12 13:55:37.335 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始执行数据采集
2025-08-12 13:55:40.544 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-12 13:55:40.544 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-12 13:55:43.693 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-12 13:55:43.693 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-12 14:04:15.833 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 14:04:16.005 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 14:04:16.134 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 14:04:16.377 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 14:04:16.378 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 14:04:16.379 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 14:04:16.381 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 14:04:16.385 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 14:04:16.397 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 14:04:16.523 [JavaFX Application Thread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@552af74b
2025-08-12 14:04:16.525 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 14:04:16.778 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionBasicFieldMapper.xml
2025-08-12 14:04:16.784 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableHeaderMapper.xml
2025-08-12 14:04:16.793 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableDataMapper.xml
2025-08-12 14:04:16.800 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 14:04:16.801 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-12 14:04:16.803 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 14:04:16.804 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 14:04:16.805 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=20
2025-08-12 14:04:16.809 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 已存在，跳过创建
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 已存在，跳过创建
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 已存在，跳过创建
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 已存在，跳过创建
2025-08-12 14:04:16.812 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-12 14:04:16.817 [JavaFX Application Thread] INFO  c.l.s.DatabaseInitializationService - 使用动态表创建服务初始化数据库表结构成功
2025-08-12 14:04:16.818 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库表结构初始化完成
2025-08-12 14:04:17.061 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-12 14:04:18.936 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-12 14:04:18.936 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-12 14:04:44.100 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-12 14:04:44.101 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
