package com.logictrue.config;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类（非Spring Boot环境）
 */
public class MyBatisPlusConfig {
    private static final Logger logger = LoggerFactory.getLogger(MyBatisPlusConfig.class);

    private static MyBatisPlusConfig instance;
    private SqlSessionFactory sqlSessionFactory;
    private DataSource dataSource;

    private MyBatisPlusConfig() {
        initializeMyBatisPlus();
    }

    /**
     * 获取单例实例
     */
    public static synchronized MyBatisPlusConfig getInstance() {
        if (instance == null) {
            instance = new MyBatisPlusConfig();
        }
        return instance;
    }

    /**
     * 初始化MyBatis-Plus
     */
    private void initializeMyBatisPlus() {
        try {
            // 创建数据源
            this.dataSource = createDataSource();

            // 创建SqlSessionFactory
            this.sqlSessionFactory = createSqlSessionFactory(dataSource);

            logger.info("MyBatis-Plus初始化成功");
        } catch (Exception e) {
            logger.error("MyBatis-Plus初始化失败", e);
            throw new RuntimeException("MyBatis-Plus初始化失败", e);
        }
    }

    /**
     * 创建数据源
     */
    private DataSource createDataSource() {
        // 直接获取数据库路径，避免循环依赖
        String dbPath = getDbPath();
        String jdbcUrl = "jdbc:sqlite:" + dbPath;

        logger.info("配置SQLite数据源，路径: {}", dbPath);

        // 配置HikariCP连接池
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setDriverClassName("org.sqlite.JDBC");

        // SQLite特定配置
        config.setMaximumPoolSize(1); // SQLite建议使用单连接
        config.setMinimumIdle(1);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);

        // SQLite连接属性
        config.addDataSourceProperty("foreign_keys", "true");
        config.addDataSourceProperty("journal_mode", "WAL");
        config.addDataSourceProperty("synchronous", "NORMAL");

        return new HikariDataSource(config);
    }

    /**
     * 获取数据库路径（避免循环依赖）
     */
    private String getDbPath() {
        try {
            String jarPath = MyBatisPlusConfig.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            String jarDir;
            if (jarFile.isFile()) {
                // 运行的是jar包
                jarDir = jarFile.getParent();
            } else {
                // 开发环境，使用项目根目录
                jarDir = System.getProperty("user.dir");
            }

            return jarDir + File.separator + "data_iot.db";
        } catch (Exception e) {
            logger.error("获取数据库路径失败", e);
            return System.getProperty("user.dir") + File.separator + "data_iot.db";
        }
    }

    /**
     * 创建SqlSessionFactory（非Spring Boot环境）
     */
    private SqlSessionFactory createSqlSessionFactory(DataSource dataSource) throws Exception {
        // 创建MyBatis-Plus配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true); // 开启驼峰命名转换
        configuration.setLogImpl(Slf4jImpl.class); // 配置日志

        // 创建环境
        Environment environment = new Environment("development", new JdbcTransactionFactory(), dataSource);
        configuration.setEnvironment(environment);

        // 添加Mapper接口
        configuration.addMapper(com.logictrue.mapper.DeviceDetectionDataMapper.class);
        configuration.addMapper(com.logictrue.mapper.DeviceDetectionBasicFieldMapper.class);
        configuration.addMapper(com.logictrue.mapper.DeviceDetectionTableHeaderMapper.class);
        configuration.addMapper(com.logictrue.mapper.DeviceDetectionTableDataMapper.class);

        // 加载XML映射文件
        loadXmlMappers(configuration);

        // 配置MyBatis-Plus全局配置
        GlobalConfig globalConfig = new GlobalConfig();

        // 配置主键生成策略
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setIdType(com.baomidou.mybatisplus.annotation.IdType.AUTO);
        dbConfig.setTableUnderline(true); // 开启下划线转驼峰
        globalConfig.setDbConfig(dbConfig);

        // 设置全局配置到Configuration
        GlobalConfigUtils.setGlobalConfig(configuration, globalConfig);

        // 使用MyBatis-Plus的SqlSessionFactoryBuilder
        return new MybatisSqlSessionFactoryBuilder().build(configuration);
    }

    /**
     * 加载XML映射文件
     */
    private void loadXmlMappers(MybatisConfiguration configuration) {
        String[] mapperXmlFiles = {
            "mapper/DeviceDetectionBasicFieldMapper.xml",
            "mapper/DeviceDetectionTableHeaderMapper.xml",
            "mapper/DeviceDetectionTableDataMapper.xml"
        };

        for (String mapperXmlFile : mapperXmlFiles) {
            try {
                InputStream inputStream = getResourceAsStream(mapperXmlFile);
                if (inputStream != null) {
                    XMLMapperBuilder xmlMapperBuilder = new XMLMapperBuilder(
                            inputStream, configuration, mapperXmlFile, configuration.getSqlFragments());
                    xmlMapperBuilder.parse();
                    logger.info("成功加载XML映射文件: {}", mapperXmlFile);
                } else {
                    logger.warn("XML映射文件不存在: {}", mapperXmlFile);
                }
            } catch (Exception e) {
                logger.error("加载XML映射文件失败: {}", mapperXmlFile, e);
            }
        }
    }

    /**
     * 兼容模块化的资源加载方法
     */
    private InputStream getResourceAsStream(String resource) {
        // 首先尝试使用当前类的类加载器
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(resource);
        if (inputStream != null) {
            return inputStream;
        }

        // 尝试使用当前类所在模块的资源加载
        inputStream = this.getClass().getResourceAsStream("/" + resource);
        if (inputStream != null) {
            return inputStream;
        }

        // 尝试使用MyBatis的Resources类（兼容性）
        try {
            inputStream = Resources.getResourceAsStream(resource);
            if (inputStream != null) {
                return inputStream;
            }
        } catch (Exception e) {
            logger.debug("使用MyBatis Resources加载失败: {}", resource, e);
        }

        // 尝试使用线程上下文类加载器
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        if (contextClassLoader != null) {
            inputStream = contextClassLoader.getResourceAsStream(resource);
            if (inputStream != null) {
                return inputStream;
            }
        }

        logger.warn("无法找到资源文件: {}", resource);
        return null;
    }

    /**
     * 获取SqlSessionFactory
     */
    public SqlSessionFactory getSqlSessionFactory() {
        return sqlSessionFactory;
    }

    /**
     * 获取SqlSession
     */
    public SqlSession getSqlSession() {
        return sqlSessionFactory.openSession(true); // 自动提交事务
    }

    /**
     * 获取数据源
     */
    public DataSource getDataSource() {
        return dataSource;
    }

    /**
     * 关闭资源
     */
    public void close() {
        try {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
                logger.info("数据源已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭数据源失败", e);
        }
    }
}
