package com.logictrue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备检测基础字段Mapper接口
 */
@Mapper
public interface DeviceDetectionBasicFieldMapper extends BaseMapper<DeviceDetectionBasicField> {

    /**
     * 根据检测数据ID查询基础字段
     */
    @Select("SELECT * FROM device_detection_basic_field " +
            "WHERE detection_data_id = #{detectionDataId} " +
            "ORDER BY sheet_id, field_code")
    List<DeviceDetectionBasicField> selectByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID和工作表ID查询基础字段
     */
    @Select("SELECT * FROM device_detection_basic_field " +
            "WHERE detection_data_id = #{detectionDataId} AND sheet_id = #{sheetId} " +
            "ORDER BY field_code")
    List<DeviceDetectionBasicField> selectByDetectionDataIdAndSheetId(@Param("detectionDataId") Long detectionDataId,
                                                                      @Param("sheetId") String sheetId);

    /**
     * 根据检测数据ID统计基础字段数量
     */
    @Select("SELECT COUNT(*) FROM device_detection_basic_field " +
            "WHERE detection_data_id = #{detectionDataId}")
    long countByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID删除基础字段
     */
    @Select("DELETE FROM device_detection_basic_field " +
            "WHERE detection_data_id = #{detectionDataId}")
    int deleteByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 批量插入基础字段数据
     * 使用XML配置的foreach标签实现真正的SQL批量插入
     */
    int batchInsert(@Param("list") List<DeviceDetectionBasicField> basicFields);

    /**
     * 批量插入基础字段数据（忽略重复）
     */
    int batchInsertIgnore(@Param("list") List<DeviceDetectionBasicField> basicFields);

    /**
     * 批量更新基础字段数据
     */
    int batchUpdate(@Param("list") List<DeviceDetectionBasicField> basicFields);

    /**
     * 批量删除基础字段数据
     */
    int batchDeleteByIds(@Param("list") List<Long> ids);
}
