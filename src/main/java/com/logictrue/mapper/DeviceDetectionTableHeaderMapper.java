package com.logictrue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备检测表头Mapper接口
 */
@Mapper
public interface DeviceDetectionTableHeaderMapper extends BaseMapper<DeviceDetectionTableHeader> {

    /**
     * 根据检测数据ID查询表头
     */
    @Select("SELECT * FROM device_detection_table_header " +
            "WHERE detection_data_id = #{detectionDataId} " +
            "ORDER BY sheet_id, column_order")
    List<DeviceDetectionTableHeader> selectByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID和工作表ID查询表头
     */
    @Select("SELECT * FROM device_detection_table_header " +
            "WHERE detection_data_id = #{detectionDataId} AND sheet_id = #{sheetId} " +
            "ORDER BY column_order")
    List<DeviceDetectionTableHeader> selectByDetectionDataIdAndSheetId(@Param("detectionDataId") Long detectionDataId,
                                                                       @Param("sheetId") String sheetId);

    /**
     * 根据检测数据ID统计表头数量
     */
    @Select("SELECT COUNT(*) FROM device_detection_table_header " +
            "WHERE detection_data_id = #{detectionDataId}")
    long countByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 根据检测数据ID删除表头
     */
    @Select("DELETE FROM device_detection_table_header " +
            "WHERE detection_data_id = #{detectionDataId}")
    int deleteByDetectionDataId(@Param("detectionDataId") Long detectionDataId);

    /**
     * 批量插入表头数据
     * 使用XML配置的foreach标签实现真正的SQL批量插入
     */
    int batchInsert(@Param("list") List<DeviceDetectionTableHeader> tableHeaders);

    /**
     * 批量插入表头数据（忽略重复）
     */
    int batchInsertIgnore(@Param("list") List<DeviceDetectionTableHeader> tableHeaders);

    /**
     * 批量更新表头数据
     */
    int batchUpdate(@Param("list") List<DeviceDetectionTableHeader> tableHeaders);

    /**
     * 批量删除表头数据
     */
    int batchDeleteByIds(@Param("list") List<Long> ids);

    /**
     * 根据检测数据ID批量删除表头数据
     */
    int batchDeleteByDetectionDataIds(@Param("list") List<Long> detectionDataIds);
}
