<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionTableDataMapper">

    <!-- 批量插入表格数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO device_detection_table_data (
            detection_data_id,
            sheet_id,
            sheet_name,
            row_order,
            column_name,
            cell_value,
            row_index,
            col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.rowOrder},
                #{item.columnName},
                #{item.cellValue},
                #{item.rowIndex},
                #{item.colIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入表格数据（忽略重复） -->
    <insert id="batchInsertIgnore" parameterType="java.util.List">
        INSERT OR IGNORE INTO device_detection_table_data (
            detection_data_id,
            sheet_id,
            sheet_name,
            row_order,
            column_name,
            cell_value,
            row_index,
            col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.rowOrder},
                #{item.columnName},
                #{item.cellValue},
                #{item.rowIndex},
                #{item.colIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 分批批量插入表格数据（适用于大数据量） -->
    <insert id="batchInsertChunk" parameterType="java.util.List">
        INSERT INTO device_detection_table_data (
            detection_data_id,
            sheet_id,
            sheet_name,
            row_order,
            column_name,
            cell_value,
            row_index,
            col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.rowOrder},
                #{item.columnName},
                #{item.cellValue},
                #{item.rowIndex},
                #{item.colIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新表格数据 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE device_detection_table_data SET
                sheet_name = #{item.sheetName},
                row_order = #{item.rowOrder},
                column_name = #{item.columnName},
                cell_value = #{item.cellValue},
                row_index = #{item.rowIndex},
                col_index = #{item.colIndex},
                update_time = #{item.updateTime}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 批量删除表格数据 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM device_detection_table_data 
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据检测数据ID批量删除表格数据 -->
    <delete id="batchDeleteByDetectionDataIds" parameterType="java.util.List">
        DELETE FROM device_detection_table_data 
        WHERE detection_data_id IN
        <foreach collection="list" item="detectionDataId" open="(" separator="," close=")">
            #{detectionDataId}
        </foreach>
    </delete>

</mapper>
