<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionTableHeaderMapper">

    <!-- 批量插入表头数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO device_detection_table_header (
            detection_data_id,
            sheet_id,
            sheet_name,
            column_name,
            column_order,
            column_type,
            row_index,
            col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.columnName},
                #{item.columnOrder},
                #{item.columnType},
                #{item.rowIndex},
                #{item.colIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入表头数据（忽略重复） -->
    <insert id="batchInsertIgnore" parameterType="java.util.List">
        INSERT OR IGNORE INTO device_detection_table_header (
            detection_data_id,
            sheet_id,
            sheet_name,
            column_name,
            column_order,
            column_type,
            row_index,
            col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.columnName},
                #{item.columnOrder},
                #{item.columnType},
                #{item.rowIndex},
                #{item.colIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新表头数据 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE device_detection_table_header SET
                sheet_name = #{item.sheetName},
                column_name = #{item.columnName},
                column_order = #{item.columnOrder},
                column_type = #{item.columnType},
                row_index = #{item.rowIndex},
                col_index = #{item.colIndex},
                update_time = #{item.updateTime}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 批量删除表头数据 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM device_detection_table_header 
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据检测数据ID批量删除表头数据 -->
    <delete id="batchDeleteByDetectionDataIds" parameterType="java.util.List">
        DELETE FROM device_detection_table_header 
        WHERE detection_data_id IN
        <foreach collection="list" item="detectionDataId" open="(" separator="," close=")">
            #{detectionDataId}
        </foreach>
    </delete>

</mapper>
